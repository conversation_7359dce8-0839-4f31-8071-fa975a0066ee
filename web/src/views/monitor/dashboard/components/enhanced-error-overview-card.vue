<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCard, NTag, NButton, NGrid, NGridItem, NProgress, NEmpty } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { fetchErrorAnalysis } from '@/service/api';

interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const router = useRouter();
const errorData = ref<Api.Monitor.ErrorAnalysisData | null>(null);
const dataLoading = ref(true);

// 计算错误统计
const errorStats = computed(() => {
  if (!errorData.value?.error_summary) {
    return {
      totalErrors: 0,
      errorRate: 0,
      criticalErrors: 0,
      topErrorType: '无',
      errorTrend: 'stable'
    };
  }

  const data = errorData.value;
  const errorsByType = data.failed_tasks?.by_type || [];

  // 找出最多的错误类型
  const topError = errorsByType.reduce((max, current) =>
    current.count > max.count ? current : max,
    { type: '无', count: 0 }
  );

  // 简化趋势计算
  const trendData = data.trend_data || [];
  const recent = trendData.slice(-3);
  const earlier = trendData.slice(-6, -3);

  const recentAvg = recent.length > 0 ?
    recent.reduce((sum, item) => sum + item.api_error_count + item.task_failure_count, 0) / recent.length : 0;
  const earlierAvg = earlier.length > 0 ?
    earlier.reduce((sum, item) => sum + item.api_error_count + item.task_failure_count, 0) / earlier.length : 0;

  let errorTrend = 'stable';
  if (recentAvg > earlierAvg * 1.2) errorTrend = 'increasing';
  else if (recentAvg < earlierAvg * 0.8) errorTrend = 'decreasing';

  return {
    totalErrors: data.error_summary.total_errors,
    errorRate: data.error_summary.error_rate * 100,
    criticalErrors: data.failed_tasks?.total || 0,
    topErrorType: topError.type,
    errorTrend
  };
});

// 获取趋势状态
function getTrendStatus(trend: string) {
  const statusMap = {
    increasing: { type: 'error' as const, text: '上升', icon: 'mdi:trending-up' },
    decreasing: { type: 'success' as const, text: '下降', icon: 'mdi:trending-down' },
    stable: { type: 'info' as const, text: '稳定', icon: 'mdi:trending-neutral' }
  };
  return statusMap[trend as keyof typeof statusMap] || statusMap.stable;
}

// 获取错误率状态
function getErrorRateStatus(rate: number) {
  if (rate <= 1) return { type: 'success' as const, text: '正常' };
  if (rate <= 5) return { type: 'warning' as const, text: '注意' };
  return { type: 'error' as const, text: '异常' };
}

// 简化的模拟数据
function generateMockData() {
  const totalErrors = Math.floor(Math.random() * 50) + 10;
  const totalRequests = Math.floor(Math.random() * 1000) + 500;

  return {
    error_summary: {
      total_errors: totalErrors,
      total_requests: totalRequests,
      error_rate: totalErrors / totalRequests,
      top_errors: [],
      top_error_endpoints: []
    },
    failed_tasks: {
      total: Math.floor(Math.random() * 10),
      total_tasks: Math.floor(Math.random() * 50) + 20,
      failure_rate: Math.random() * 0.1,
      by_type: [
        { type: 'STRM生成', count: Math.floor(Math.random() * 5) },
        { type: '资源下载', count: Math.floor(Math.random() * 3) }
      ]
    },
    trend_data: [],
    analysis_period_hours: 24
  };
}

// 加载错误数据
async function loadErrorData() {
  try {
    dataLoading.value = true;
    const response = await fetchErrorAnalysis({ hours: 24 });
    errorData.value = response.data;
  } catch (error) {
    console.warn('获取错误数据失败，使用模拟数据:', error);
    errorData.value = generateMockData() as any;
  } finally {
    dataLoading.value = false;
  }
}

onMounted(() => {
  loadErrorData();
});

function goToErrorDetail() {
  router.push('/monitor/errors');
}

// 获取错误率状态
function getErrorRateStatus(errorRate: number) {
  if (errorRate <= 1) {
    return {
      type: 'success',
      text: '良好',
      color: '#10b981'
    };
  }
  if (errorRate <= 5) {
    return {
      type: 'warning',
      text: '警告',
      color: '#f59e0b'
    };
  }
  return {
    type: 'error',
    text: '严重',
    color: '#ef4444'
  };
}

// 获取趋势状态
function getTrendStatus(trend: string) {
  switch (trend) {
    case 'up':
      return {
        type: 'error',
        text: '上升',
        icon: 'mdi:trending-up'
      };
    case 'down':
      return {
        type: 'success',
        text: '下降',
        icon: 'mdi:trending-down'
      };
    default:
      return {
        type: 'info',
        text: '稳定',
        icon: 'mdi:trending-neutral'
      };
  }
}
</script>

<template>
  <NCard class="modern-error-card">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <div class="header-icon">
            <Icon icon="mdi:shield-alert" />
            <div class="icon-glow"></div>
          </div>
          <div class="header-content">
            <h3 class="header-title">
              错误分析
            </h3>
            <div class="header-status">
              <div class="status-indicator">
                <div class="status-dot" :style="{ backgroundColor: getErrorRateStatus(errorStats.errorRate).color }"></div>
                <span class="status-text">{{ getErrorRateStatus(errorStats.errorRate).text }}</span>
                <div class="status-wave" :style="{ backgroundColor: getErrorRateStatus(errorStats.errorRate).color }"></div>
              </div>
            </div>
          </div>
        </div>
        <NButton text @click="goToErrorDetail" class="detail-btn">
          <template #icon>
            <Icon icon="mdi:arrow-top-right" />
          </template>
          详情
        </NButton>
      </div>
    </template>

    <div v-if="dataLoading" class="loading-state">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p class="loading-text">分析错误数据中...</p>
    </div>

    <div v-else-if="!errorData" class="empty-state">
      <Icon icon="mdi:database-off" class="empty-icon" />
      <p class="empty-text">暂无错误数据</p>
    </div>

    <div v-else class="card-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 中央错误率显示 -->
        <div class="central-section">
          <div class="error-gauge">
            <!-- SVG环形进度条 -->
            <div class="gauge-wrapper">
              <svg class="circular-progress" viewBox="0 0 200 200">
                <!-- 定义渐变 -->
                <defs>
                  <linearGradient :id="`error-gradient-${getErrorRateStatus(errorStats.errorRate).type}`" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" :style="`stop-color:${getErrorRateStatus(errorStats.errorRate).color};stop-opacity:1`" />
                    <stop offset="100%" :style="`stop-color:${getErrorRateStatus(errorStats.errorRate).color};stop-opacity:0.6`" />
                  </linearGradient>
                </defs>

                <!-- 背景圆环 -->
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="rgba(226, 232, 240, 0.3)"
                  stroke-width="8"
                />

                <!-- 进度圆环 -->
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  :stroke="`url(#error-gradient-${getErrorRateStatus(errorStats.errorRate).type})`"
                  stroke-width="8"
                  stroke-linecap="round"
                  :stroke-dasharray="`${Math.min(errorStats.errorRate * 5, 100) * 5.02} 502`"
                  transform="rotate(-90 100 100)"
                  class="progress-ring"
                />
              </svg>

              <!-- 中央数值 -->
              <div class="gauge-center">
                <div class="center-value" :style="{ color: getErrorRateStatus(errorStats.errorRate).color }">
                  {{ errorStats.errorRate.toFixed(1) }}%
                </div>
                <div class="center-label">错误率</div>
                <div class="center-status" :class="`status-${getErrorRateStatus(errorStats.errorRate).type}`">
                  <Icon :icon="getErrorRateStatus(errorStats.errorRate).type === 'error' ? 'mdi:alert-circle' : 'mdi:check-circle'" class="status-icon" />
                  {{ getErrorRateStatus(errorStats.errorRate).text }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边指标 -->
        <div class="side-metrics">
          <div class="metric-item total-errors">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:alert-circle" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ errorStats.totalErrors }}</div>
              <div class="metric-text">总错误数</div>
            </div>
          </div>

          <div class="metric-item critical-errors">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:alert-octagon" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ errorStats.criticalErrors }}</div>
              <div class="metric-text">严重错误</div>
            </div>
          </div>

          <div class="metric-item error-type">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:tag-multiple" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ errorStats.topErrorType }}</div>
              <div class="metric-text">主要类型</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势指示器 -->
      <div class="trend-section">
        <div class="trend-label">错误趋势</div>
        <div class="trend-indicator" :class="`trend-${getTrendStatus(errorStats.errorTrend).type}`">
          <Icon :icon="getTrendStatus(errorStats.errorTrend).icon" class="trend-icon" />
          <span class="trend-text">{{ getTrendStatus(errorStats.errorTrend).text }}</span>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.modern-error-card {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modern-error-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.4);
}

.icon-glow {
  position: absolute;
  inset: -2px;
  border-radius: 18px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  opacity: 0.3;
  filter: blur(8px);
  z-index: -1;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.status-wave {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.6;
  animation: statusWave 2s ease-in-out infinite;
}

.detail-btn {
  color: #3b82f6;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #ef4444;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: #f97316;
  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: #eab308;
  animation-delay: -0.6s;
}

.loading-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 12px;
}

.empty-icon {
  font-size: 48px;
  color: #cbd5e1;
}

.empty-text {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

/* 主要内容 */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.main-content {
  display: flex;
  gap: 20px;
  flex: 1;
}

.central-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-gauge {
  position: relative;
  width: 160px;
  height: 160px;
}

.gauge-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.circular-progress {
  width: 100%;
  height: 100%;
  transform: rotate(0deg);
}

.progress-ring {
  transition: stroke-dasharray 1s ease-in-out;
  filter: drop-shadow(0 0 8px rgba(239, 68, 68, 0.3));
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
}

.center-value {
  font-size: 24px;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 4px;
}

.center-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8px;
}

.center-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.center-status.status-success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.center-status.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.center-status.status-error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-icon {
  font-size: 10px;
}

/* 侧边指标 */
.side-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 140px;
}

.metric-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 16px;
  background: white;
  border: 1px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 为每个指标添加独特的边框和背景色 */
.metric-item.total-errors {
  border: 1px solid #ef4444 !important;
  background: white;
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.1);
}

.metric-item.critical-errors {
  border: 1px solid #f97316 !important;
  background: white;
  box-shadow: 0 4px 20px rgba(249, 115, 22, 0.1);
}

.metric-item.error-type {
  border: 1px solid #8b5cf6 !important;
  background: white;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.1);
}

.metric-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.4;
  transition: all 0.3s ease;
  border-radius: 16px;
  background: transparent;
}

.metric-item:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.metric-item:hover .metric-background {
  opacity: 0.6;
  transform: scale(1.02);
}

/* 每个指标的独特悬停效果 */
.metric-item.total-errors:hover {
  box-shadow: 0 20px 60px rgba(239, 68, 68, 0.4);
  border-color: #ef4444 !important;
}

.metric-item.critical-errors:hover {
  box-shadow: 0 20px 60px rgba(249, 115, 22, 0.4);
  border-color: #f97316 !important;
}

.metric-item.error-type:hover {
  box-shadow: 0 20px 60px rgba(139, 92, 246, 0.4);
  border-color: #8b5cf6 !important;
}

.metric-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 总错误数图标 - 红色 */
.metric-item.total-errors .metric-icon-wrapper {
  background: #ef4444;
  box-shadow:
    0 4px 15px rgba(239, 68, 68, 0.6),
    0 0 20px rgba(220, 38, 38, 0.4);
}

/* 严重错误图标 - 橙色 */
.metric-item.critical-errors .metric-icon-wrapper {
  background: #f97316;
  box-shadow:
    0 4px 15px rgba(249, 115, 22, 0.6),
    0 0 20px rgba(234, 88, 12, 0.4);
}

/* 错误类型图标 - 紫色 */
.metric-item.error-type .metric-icon-wrapper {
  background: #8b5cf6;
  box-shadow:
    0 4px 15px rgba(139, 92, 246, 0.6),
    0 0 20px rgba(124, 58, 237, 0.4);
}

.metric-icon {
  color: white;
  font-size: 18px;
  z-index: 2;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-number {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  margin-bottom: 2px;
}

.metric-text {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  line-height: 1.2;
}

/* 趋势部分 */
.trend-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.trend-label {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.trend-indicator.trend-success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.trend-indicator.trend-error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.trend-indicator.trend-info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.trend-icon {
  font-size: 14px;
}

.trend-text {
  font-size: 11px;
}

/* 动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes statusWave {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
  }

  .side-metrics {
    flex-direction: row;
    min-width: auto;
    overflow-x: auto;
  }

  .metric-item {
    min-width: 120px;
  }

  .error-gauge {
    width: 120px;
    height: 120px;
  }

  .center-value {
    font-size: 20px;
  }
}
</style>
