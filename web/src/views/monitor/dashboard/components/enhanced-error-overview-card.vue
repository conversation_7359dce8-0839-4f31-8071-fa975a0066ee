<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCard, NTag, NButton, NGrid, NGridItem, NProgress, NEmpty } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { fetchErrorAnalysis } from '@/service/api';

interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const router = useRouter();
const errorData = ref<Api.Monitor.ErrorAnalysisData | null>(null);
const dataLoading = ref(true);

// 计算错误统计
const errorStats = computed(() => {
  if (!errorData.value?.error_summary) {
    return {
      totalErrors: 0,
      errorRate: 0,
      criticalErrors: 0,
      topErrorType: '无',
      errorTrend: 'stable'
    };
  }

  const data = errorData.value;
  const errorsByType = data.failed_tasks?.by_type || [];

  // 找出最多的错误类型
  const topError = errorsByType.reduce((max, current) =>
    current.count > max.count ? current : max,
    { type: '无', count: 0 }
  );

  // 简化趋势计算
  const trendData = data.trend_data || [];
  const recent = trendData.slice(-3);
  const earlier = trendData.slice(-6, -3);

  const recentAvg = recent.length > 0 ?
    recent.reduce((sum, item) => sum + item.api_error_count + item.task_failure_count, 0) / recent.length : 0;
  const earlierAvg = earlier.length > 0 ?
    earlier.reduce((sum, item) => sum + item.api_error_count + item.task_failure_count, 0) / earlier.length : 0;

  let errorTrend = 'stable';
  if (recentAvg > earlierAvg * 1.2) errorTrend = 'increasing';
  else if (recentAvg < earlierAvg * 0.8) errorTrend = 'decreasing';

  return {
    totalErrors: data.error_summary.total_errors,
    errorRate: data.error_summary.error_rate * 100,
    criticalErrors: data.failed_tasks?.total || 0,
    topErrorType: topError.type,
    errorTrend
  };
});

// 获取趋势状态
function getTrendStatus(trend: string) {
  const statusMap = {
    increasing: { type: 'error' as const, text: '上升', icon: 'mdi:trending-up' },
    decreasing: { type: 'success' as const, text: '下降', icon: 'mdi:trending-down' },
    stable: { type: 'info' as const, text: '稳定', icon: 'mdi:trending-neutral' }
  };
  return statusMap[trend as keyof typeof statusMap] || statusMap.stable;
}

// 获取错误率状态
function getErrorRateStatus(rate: number) {
  if (rate <= 1) return {
    type: 'success' as const,
    text: '良好',
    color: '#10b981'
  };
  if (rate <= 5) return {
    type: 'warning' as const,
    text: '警告',
    color: '#f59e0b'
  };
  return {
    type: 'error' as const,
    text: '严重',
    color: '#ef4444'
  };
}

// 简化的模拟数据
function generateMockData() {
  const totalErrors = Math.floor(Math.random() * 50) + 10;
  const totalRequests = Math.floor(Math.random() * 1000) + 500;

  return {
    error_summary: {
      total_errors: totalErrors,
      total_requests: totalRequests,
      error_rate: totalErrors / totalRequests,
      top_errors: [],
      top_error_endpoints: []
    },
    failed_tasks: {
      total: Math.floor(Math.random() * 10),
      total_tasks: Math.floor(Math.random() * 50) + 20,
      failure_rate: Math.random() * 0.1,
      by_type: [
        { type: 'STRM生成', count: Math.floor(Math.random() * 5) },
        { type: '资源下载', count: Math.floor(Math.random() * 3) }
      ]
    },
    trend_data: [],
    analysis_period_hours: 24
  };
}

// 加载错误数据
async function loadErrorData() {
  try {
    dataLoading.value = true;
    const response = await fetchErrorAnalysis({ hours: 24 });
    errorData.value = response.data;
  } catch (error) {
    console.warn('获取错误数据失败，使用模拟数据:', error);
    errorData.value = generateMockData() as any;
  } finally {
    dataLoading.value = false;
  }
}

onMounted(() => {
  loadErrorData();
});

function goToErrorDetail() {
  router.push('/monitor/errors');
}
</script>

<template>
  <NCard class="modern-error-card">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <div class="header-icon">
            <Icon icon="mdi:shield-alert" />
            <div class="icon-glow"></div>
          </div>
          <div class="header-content">
            <h3 class="header-title">
              错误分析
            </h3>
            <div class="header-status">
              <div class="status-indicator">
                <div class="status-dot" :style="{ backgroundColor: getErrorRateStatus(errorStats.errorRate).color }"></div>
                <span class="status-text">{{ getErrorRateStatus(errorStats.errorRate).text }}</span>
                <div class="status-wave" :style="{ backgroundColor: getErrorRateStatus(errorStats.errorRate).color }"></div>
              </div>
            </div>
          </div>
        </div>
        <NButton text @click="goToErrorDetail" class="detail-btn">
          <template #icon>
            <Icon icon="mdi:arrow-top-right" />
          </template>
          详情
        </NButton>
      </div>
    </template>

    <div v-if="dataLoading" class="loading-state">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p class="loading-text">分析错误数据中...</p>
    </div>

    <div v-else-if="!errorData" class="empty-state">
      <Icon icon="mdi:database-off" class="empty-icon" />
      <p class="empty-text">暂无错误数据</p>
    </div>

    <div v-else class="card-content">
      <!-- 错误率概览 -->
      <div class="error-overview">
        <div class="error-rate-display">
          <div class="rate-circle" :class="`rate-${getErrorRateStatus(errorStats.errorRate).type}`">
            <div class="rate-value">{{ errorStats.errorRate.toFixed(1) }}%</div>
            <div class="rate-label">错误率</div>
          </div>
          <div class="rate-status">
            <Icon :icon="getErrorRateStatus(errorStats.errorRate).type === 'error' ? 'mdi:alert-circle' : 'mdi:check-circle'" />
            {{ getErrorRateStatus(errorStats.errorRate).text }}
          </div>
        </div>

        <div class="error-summary">
          <div class="summary-item">
            <div class="summary-value">{{ errorStats.totalErrors }}</div>
            <div class="summary-label">总错误数</div>
          </div>
          <div class="summary-divider"></div>
          <div class="summary-item">
            <div class="summary-value">{{ errorStats.criticalErrors }}</div>
            <div class="summary-label">严重错误</div>
          </div>
        </div>
      </div>

      <!-- 错误详情网格 -->
      <div class="error-details">
        <div class="detail-card error-types">
          <div class="detail-header">
            <Icon icon="mdi:tag-multiple" class="detail-icon" />
            <span class="detail-title">主要类型</span>
          </div>
          <div class="detail-content">
            <div class="type-badge">{{ errorStats.topErrorType }}</div>
          </div>
        </div>

        <div class="detail-card error-trend">
          <div class="detail-header">
            <Icon icon="mdi:chart-line" class="detail-icon" />
            <span class="detail-title">趋势分析</span>
          </div>
          <div class="detail-content">
            <div class="trend-badge" :class="`trend-${getTrendStatus(errorStats.errorTrend).type}`">
              <Icon :icon="getTrendStatus(errorStats.errorTrend).icon" class="trend-icon" />
              {{ getTrendStatus(errorStats.errorTrend).text }}
            </div>
          </div>
        </div>

        <div class="detail-card error-distribution">
          <div class="detail-header">
            <Icon icon="mdi:chart-donut" class="detail-icon" />
            <span class="detail-title">错误分布</span>
          </div>
          <div class="detail-content">
            <div class="distribution-chart">
              <!-- 环形图表 -->
              <div class="donut-chart">
                <svg viewBox="0 0 100 100" class="donut-svg">
                  <!-- API错误弧形 -->
                  <circle
                    cx="50"
                    cy="50"
                    r="35"
                    fill="none"
                    stroke="#ef4444"
                    stroke-width="12"
                    stroke-dasharray="70 30"
                    stroke-dashoffset="25"
                    class="donut-segment api-segment"
                  />
                  <!-- 任务错误弧形 -->
                  <circle
                    cx="50"
                    cy="50"
                    r="35"
                    fill="none"
                    stroke="#f59e0b"
                    stroke-width="12"
                    stroke-dasharray="30 70"
                    stroke-dashoffset="-45"
                    class="donut-segment task-segment"
                  />
                </svg>
                <div class="donut-center">
                  <div class="center-number">{{ errorStats.totalErrors }}</div>
                  <div class="center-text">总计</div>
                </div>
              </div>

              <!-- 图例 -->
              <div class="distribution-legend">
                <div class="legend-item">
                  <div class="legend-dot api-dot"></div>
                  <div class="legend-info">
                    <div class="legend-label">API错误</div>
                    <div class="legend-value">{{ Math.floor(errorStats.totalErrors * 0.7) }}</div>
                  </div>
                  <div class="legend-percent">70%</div>
                </div>
                <div class="legend-item">
                  <div class="legend-dot task-dot"></div>
                  <div class="legend-info">
                    <div class="legend-label">任务错误</div>
                    <div class="legend-value">{{ Math.floor(errorStats.totalErrors * 0.3) }}</div>
                  </div>
                  <div class="legend-percent">30%</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="detail-card error-timeline">
          <div class="detail-header">
            <Icon icon="mdi:clock-outline" class="detail-icon" />
            <span class="detail-title">24小时统计</span>
          </div>
          <div class="detail-content">
            <div class="timeline-stats">
              <div class="stat-point high">
                <div class="point-value">{{ Math.floor(errorStats.totalErrors * 0.4) }}</div>
                <div class="point-label">高峰期</div>
              </div>
              <div class="stat-point low">
                <div class="point-value">{{ Math.floor(errorStats.totalErrors * 0.1) }}</div>
                <div class="point-label">低谷期</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.modern-error-card {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modern-error-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.4);
}

.icon-glow {
  position: absolute;
  inset: -2px;
  border-radius: 18px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  opacity: 0.3;
  filter: blur(8px);
  z-index: -1;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.status-wave {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.6;
  animation: statusWave 2s ease-in-out infinite;
}

.detail-btn {
  color: #3b82f6;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #ef4444;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: #f97316;
  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: #eab308;
  animation-delay: -0.6s;
}

.loading-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 12px;
}

.empty-icon {
  font-size: 48px;
  color: #cbd5e1;
}

.empty-text {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

/* 主要内容 */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

/* 错误率概览 */
.error-overview {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.error-rate-display {
  display: flex;
  align-items: center;
  gap: 16px;
}

.rate-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 3px solid;
  background: white;
}

.rate-circle.rate-success {
  border-color: #10b981;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.rate-circle.rate-warning {
  border-color: #f59e0b;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.rate-circle.rate-error {
  border-color: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.rate-value {
  font-size: 20px;
  font-weight: 800;
  line-height: 1;
}

.rate-circle.rate-success .rate-value {
  color: #10b981;
}

.rate-circle.rate-warning .rate-value {
  color: #f59e0b;
}

.rate-circle.rate-error .rate-value {
  color: #ef4444;
}

.rate-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  margin-top: 2px;
}

.rate-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
}

.error-summary {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.summary-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  margin-top: 2px;
}

.summary-divider {
  width: 1px;
  height: 30px;
  background: rgba(226, 232, 240, 0.8);
}

/* 错误详情网格 */
.error-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-card {
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-icon {
  font-size: 16px;
  color: #64748b;
}

.detail-title {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 类型徽章 */
.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
  width: fit-content;
}

/* 趋势徽章 */
.trend-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  width: fit-content;
}

.trend-badge.trend-success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.trend-badge.trend-error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.trend-badge.trend-info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.trend-icon {
  font-size: 14px;
}

/* 分布环形图 */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.donut-chart {
  position: relative;
  width: 80px;
  height: 80px;
}

.donut-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.donut-segment {
  transition: all 0.8s ease;
  stroke-linecap: round;
}

.api-segment {
  animation: drawApiSegment 1.5s ease-out;
}

.task-segment {
  animation: drawTaskSegment 1.5s ease-out 0.3s both;
}

.donut-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.center-number {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.center-text {
  font-size: 9px;
  color: #64748b;
  font-weight: 500;
  margin-top: 1px;
}

/* 图例 */
.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.4);
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-dot.api-dot {
  background: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.legend-dot.task-dot {
  background: #f59e0b;
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.legend-info {
  flex: 1;
  min-width: 0;
}

.legend-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  line-height: 1;
}

.legend-value {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1;
  margin-top: 1px;
}

.legend-percent {
  font-size: 10px;
  font-weight: 600;
  color: #64748b;
  background: white;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

/* 动画 */
@keyframes drawApiSegment {
  from {
    stroke-dasharray: 0 100;
  }
  to {
    stroke-dasharray: 70 30;
  }
}

@keyframes drawTaskSegment {
  from {
    stroke-dasharray: 0 100;
  }
  to {
    stroke-dasharray: 30 70;
  }
}

/* 时间线统计 */
.timeline-stats {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.stat-point {
  text-align: center;
  flex: 1;
}

.point-value {
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.stat-point.high .point-value {
  color: #ef4444;
}

.stat-point.low .point-value {
  color: #10b981;
}

.point-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-overview {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .error-rate-display {
    width: 100%;
    justify-content: space-between;
  }

  .error-summary {
    margin-left: 0;
    width: 100%;
    justify-content: space-around;
  }

  .error-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .rate-circle {
    width: 60px;
    height: 60px;
  }

  .rate-value {
    font-size: 16px;
  }

  .summary-value {
    font-size: 16px;
  }
}

/* 动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes statusWave {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}


</style>
