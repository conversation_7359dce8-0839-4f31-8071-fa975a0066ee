<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { NCard, NTag, NButton } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';


interface Props {
  performanceData?: Api.Monitor.PerformanceData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  performanceData: null,
  loading: false
});

const router = useRouter();

// 计算性能统计
const performanceStats = computed(() => {
  if (!props.performanceData) {
    return {
      avgResponseTime: 0,
      requestCount: 0,
      errorRate: 0,
      requestsPerMinute: 0
    };
  }

  const api = props.performanceData.api_performance;
  const rawErrorRate = api.error_rate || 0;
  const errorRate = rawErrorRate <= 1 ? rawErrorRate * 100 : rawErrorRate;

  return {
    avgResponseTime: api.avg_response_time * 1000, // 转换为毫秒
    requestCount: api.request_count,
    errorRate: Math.min(errorRate, 100),
    requestsPerMinute: api.requests_per_minute
  };
});

// 响应时间状态
const responseTimeStatus = computed(() => {
  const avgTime = performanceStats.value.avgResponseTime;
  if (avgTime < 1000) {
    return { type: 'success' as const, text: '优秀', color: '#10b981' };
  } else if (avgTime < 3000) {
    return { type: 'warning' as const, text: '良好', color: '#f59e0b' };
  } else {
    return { type: 'error' as const, text: '较慢', color: '#ef4444' };
  }
});

// 错误率状态
const errorRateStatus = computed(() => {
  const rate = performanceStats.value.errorRate;
  if (rate < 1) {
    return { type: 'success' as const, text: '优秀', color: '#10b981' };
  } else if (rate < 5) {
    return { type: 'warning' as const, text: '良好', color: '#f59e0b' };
  } else {
    return { type: 'error' as const, text: '较高', color: '#ef4444' };
  }
});

// 获取状态图标
function getStatusIcon(type: string) {
  switch (type) {
    case 'success':
      return 'mdi:check-circle';
    case 'warning':
      return 'mdi:alert-circle';
    case 'error':
      return 'mdi:close-circle';
    default:
      return 'mdi:information-circle';
  }
}

function goToPerformanceDetail() {
  router.push('/monitor/performance');
}
</script>

<template>
  <NCard class="modern-performance-card">


    <template #header>
      <div class="card-header">
        <div class="header-left">
          <div class="header-icon">
            <Icon icon="mdi:speedometer" />
            <div class="icon-glow"></div>
          </div>
          <div class="header-content">
            <h3 class="header-title">
              接口性能监控
            </h3>
            <div class="header-status">
              <div class="status-indicator">
                <div class="status-dot" :style="{ backgroundColor: responseTimeStatus.color }"></div>
                <span class="status-text">{{ responseTimeStatus.text }}</span>
                <div class="status-wave" :style="{ backgroundColor: responseTimeStatus.color }"></div>
              </div>
            </div>
          </div>
        </div>
        <NButton text @click="goToPerformanceDetail" class="detail-btn">
          <template #icon>
            <Icon icon="mdi:arrow-top-right" />
          </template>
          详情
        </NButton>
      </div>
    </template>

    <div class="card-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 现代环形进度仪表板 -->
        <div class="chart-section">
          <div class="modern-gauge-container">
            <!-- SVG环形进度条 -->
            <div class="gauge-wrapper">
              <svg class="circular-progress" viewBox="0 0 200 200">
                <!-- 定义渐变和滤镜 -->
                <defs>
                  <linearGradient :id="`gradient-${responseTimeStatus.type}`" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" :style="`stop-color:${responseTimeStatus.color};stop-opacity:1`" />
                    <stop offset="100%" :style="`stop-color:${responseTimeStatus.color};stop-opacity:0.6`" />
                  </linearGradient>
                  <filter :id="`glow-${responseTimeStatus.type}`">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                  </filter>
                  <filter id="shadow">
                    <feDropShadow dx="0" dy="2" stdDeviation="4" flood-opacity="0.3"/>
                  </filter>
                </defs>

                <!-- 背景圆环 -->
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="rgba(226, 232, 240, 0.3)"
                  stroke-width="8"
                  stroke-linecap="round"
                />

                <!-- 进度圆环 -->
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  :stroke="`url(#gradient-${responseTimeStatus.type})`"
                  stroke-width="8"
                  stroke-linecap="round"
                  :stroke-dasharray="502.4"
                  :stroke-dashoffset="502.4 - (Math.min((performanceStats.avgResponseTime / 5000) * 100, 100) / 100) * 502.4"
                  :filter="`url(#glow-${responseTimeStatus.type})`"
                  class="progress-ring"
                  transform="rotate(-90 100 100)"
                />

                <!-- 装饰性刻度 -->
                <g class="gauge-ticks">
                  <line v-for="i in 12" :key="i"
                    :x1="100 + 75 * Math.cos((i - 1) * 30 * Math.PI / 180)"
                    :y1="100 + 75 * Math.sin((i - 1) * 30 * Math.PI / 180)"
                    :x2="100 + 85 * Math.cos((i - 1) * 30 * Math.PI / 180)"
                    :y2="100 + 85 * Math.sin((i - 1) * 30 * Math.PI / 180)"
                    stroke="rgba(148, 163, 184, 0.4)"
                    stroke-width="2"
                    stroke-linecap="round"
                    :class="{ 'tick-active': i <= Math.ceil((performanceStats.avgResponseTime / 5000) * 12) }"
                  />
                </g>
              </svg>

              <!-- 中心内容 -->
              <div class="gauge-center-content">
                <div class="center-value" :style="{ color: responseTimeStatus.color }">
                  {{ performanceStats.avgResponseTime.toFixed(0) }}
                </div>
                <div class="center-unit">ms</div>
                <div class="center-status" :class="`status-${responseTimeStatus.type}`">
                  <Icon :icon="getStatusIcon(responseTimeStatus.type)" class="status-icon" />
                  {{ responseTimeStatus.text }}
                </div>
                <div class="center-percentage">
                  {{ Math.min((performanceStats.avgResponseTime / 5000) * 100, 100).toFixed(0) }}%
                </div>
              </div>
            </div>

            <!-- 底部标签 -->
            <div class="gauge-title">
              <Icon icon="mdi:speedometer-medium" class="title-icon" />
              <span>响应时间监控</span>
            </div>
          </div>
        </div>

        <!-- 指标网格 -->
        <div class="metrics-grid">
          <!-- 总请求数 -->
          <div class="metric-item requests">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:counter" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestCount.toLocaleString() }}</div>
              <div class="metric-label">总请求数 (1小时)</div>
            </div>
            <div class="metric-trend">
              <Icon icon="mdi:trending-up" />
            </div>
          </div>

          <!-- 错误率 -->
          <div class="metric-item errors" :class="errorRateStatus.type">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:alert-circle" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.errorRate.toFixed(1) }}%</div>
              <div class="metric-label">错误率 (1小时)</div>
              <NTag :type="errorRateStatus.type" size="small" class="metric-tag">
                {{ errorRateStatus.text }}
              </NTag>
            </div>
          </div>

          <!-- 每分钟请求数 -->
          <div class="metric-item rpm">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:clock-fast" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestsPerMinute.toFixed(0) }}</div>
              <div class="metric-label">请求/分钟</div>
            </div>
            <div class="metric-pulse"></div>
          </div>

          <!-- 平均响应时间 -->
          <div class="metric-item response-time" :class="responseTimeStatus.type">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:timer-outline" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.avgResponseTime.toFixed(0) }}ms</div>
              <div class="metric-label">平均响应 (1小时)</div>
              <NTag :type="responseTimeStatus.type" size="small" class="metric-tag">
                {{ responseTimeStatus.text }}
              </NTag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.modern-performance-card {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modern-performance-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.header-icon:hover {
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
}

.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 12px;
  animation: iconGlow 3s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  animation: statusPulse 2s infinite;
  box-shadow: 0 0 10px currentColor;
}

.status-wave {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  opacity: 0.3;
  animation: statusWave 2s infinite;
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes statusWave {
  0% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(3); opacity: 0; }
}

.status-text {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-btn {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.card-content {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: center;
}

.chart-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* 现代环形进度仪表板样式 */
.modern-gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.gauge-wrapper {
  position: relative;
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circular-progress {
  width: 100%;
  height: 100%;
  transform: scale(1);
  transition: transform 0.3s ease;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.1));
}

.gauge-wrapper:hover .circular-progress {
  transform: scale(1.05);
}

.progress-ring {
  transition: stroke-dashoffset 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: progressPulse 3s ease-in-out infinite;
}

@keyframes progressPulse {
  0%, 100% {
    stroke-width: 8;
    filter: url(#glow-success);
  }
  50% {
    stroke-width: 10;
    filter: url(#glow-success) brightness(1.2);
  }
}

.gauge-ticks line {
  transition: all 0.3s ease;
}

.gauge-ticks .tick-active {
  stroke: rgba(59, 130, 246, 0.8);
  stroke-width: 3;
  animation: tickGlow 2s ease-in-out infinite;
}

@keyframes tickGlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.gauge-center-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.center-value {
  font-size: 28px;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: valueFloat 3s ease-in-out infinite;
}

@keyframes valueFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.center-unit {
  font-size: 11px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 2px;
}

.center-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 6px;
  padding: 3px 8px;
  border-radius: 12px;
  backdrop-filter: blur(5px);
}

.center-status.status-success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.center-status.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.center-status.status-error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-icon {
  font-size: 12px;
  animation: statusIconBounce 2s ease-in-out infinite;
}

@keyframes statusIconBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.center-percentage {
  font-size: 9px;
  color: #9ca3af;
  font-weight: 500;
  margin-top: 4px;
  opacity: 0.8;
}

.gauge-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #374151;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.9));
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.gauge-title:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.title-icon {
  font-size: 16px;
  color: #3b82f6;
  animation: titleIconRotate 4s linear infinite;
}

@keyframes titleIconRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 16px;
  background: white;
  border: 1px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 为每个卡片添加独特的边框和背景色 - 与图标颜色保持一致 */
.metric-item.requests {
  border: 1px solid #059669;
  background: white;
  box-shadow: 0 4px 20px rgba(5, 150, 105, 0.1);
}

.metric-item.errors {
  border: 1px solid #f97316;
  background: white;
  box-shadow: 0 4px 20px rgba(249, 115, 22, 0.1);
}

.metric-item.rpm {
  border: 1px solid #7c3aed;
  background: white;
  box-shadow: 0 4px 20px rgba(124, 58, 237, 0.1);
}

.metric-item.response-time {
  border: 1px solid #0ea5e9;
  background: white;
  box-shadow: 0 4px 20px rgba(14, 165, 233, 0.1);
}

.metric-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.4;
  transition: all 0.3s ease;
  border-radius: 16px;
}

.metric-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
}

/* 移除背景色 - 使用透明背景 */
.metric-item.requests .metric-background {
  background: transparent;
}

.metric-item.errors .metric-background {
  background: transparent;
}

.metric-item.rpm .metric-background {
  background: transparent;
}

.metric-item.response-time .metric-background {
  background: transparent;
}

.metric-item:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.metric-item:hover .metric-background {
  opacity: 0.6;
  transform: scale(1.02);
}

/* 每个卡片的独特悬停效果 - 与图标颜色保持一致 */
.metric-item.requests:hover {
  box-shadow: 0 20px 60px rgba(5, 150, 105, 0.4);
  border-color: #059669;
}

.metric-item.errors:hover {
  box-shadow: 0 20px 60px rgba(249, 115, 22, 0.4);
  border-color: #f97316;
}

.metric-item.rpm:hover {
  box-shadow: 0 20px 60px rgba(124, 58, 237, 0.4);
  border-color: #7c3aed;
}

.metric-item.response-time:hover {
  box-shadow: 0 20px 60px rgba(14, 165, 233, 0.4);
  border-color: #0ea5e9;
}

.metric-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 总请求数图标 - 翠绿活力色 */
.metric-item.requests .metric-icon-wrapper {
  background: #059669;
  box-shadow:
    0 4px 15px rgba(5, 150, 105, 0.6),
    0 0 20px rgba(4, 120, 87, 0.4);
}

/* 错误率图标 - 温和橙红色 */
.metric-item.errors .metric-icon-wrapper {
  background: #f97316;
  box-shadow:
    0 4px 15px rgba(249, 115, 22, 0.6),
    0 0 20px rgba(234, 88, 12, 0.4);
}

/* 请求/分钟图标 - 紫色优雅色 */
.metric-item.rpm .metric-icon-wrapper {
  background: #7c3aed;
  box-shadow:
    0 4px 15px rgba(124, 58, 237, 0.6),
    0 0 20px rgba(109, 40, 217, 0.4);
}

/* 平均响应时间图标 - 优雅青蓝色 */
.metric-item.response-time .metric-icon-wrapper {
  background: #0ea5e9;
  box-shadow:
    0 4px 15px rgba(14, 165, 233, 0.6),
    0 0 20px rgba(2, 132, 199, 0.4);
}

.metric-icon {
  font-size: 20px;
  color: white;
  z-index: 2;
  animation: iconFloat 3s ease-in-out infinite;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* 为不同卡片的图标添加特定颜色 */
.metric-item.requests .metric-icon {
  color: #ffffff;
}

.metric-item.errors .metric-icon {
  color: #ffffff;
}

.metric-item.rpm .metric-icon {
  color: #ffffff;
}

.metric-item.response-time .metric-icon {
  color: #ffffff;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-2px) rotate(5deg); }
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  transition: all 0.3s ease;
}

.metric-value:hover {
  transform: scale(1.1);
}

.metric-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-tag {
  margin-top: 4px;
  font-size: 10px;
  font-weight: 600;
}

.metric-trend {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 14px;
  color: #667eea;
  animation: trendBounce 2s ease-in-out infinite;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

@keyframes trendBounce {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    color: #667eea;
  }
  50% {
    transform: translateY(-3px) rotate(10deg);
    color: #764ba2;
  }
}

.metric-pulse {
  position: absolute;
  top: 50%;
  right: 12px;
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #26d0ce, #1a2980);
  border-radius: 50%;
  animation: metricPulse 2s infinite;
  box-shadow: 0 0 10px rgba(38, 208, 206, 0.6);
}

@keyframes metricPulse {
  0% { transform: translateY(-50%) scale(1); opacity: 1; }
  100% { transform: translateY(-50%) scale(2); opacity: 0; }
}

/* 数据更新动画 */
.modern-performance-card.data-updated {
  animation: cardDataUpdate 1s ease-out;
}

.modern-performance-card.data-updated .metric-value {
  animation: valueUpdate 1.2s ease-out;
}

.modern-performance-card.data-updated .chart-center-text {
  animation: chartUpdate 1s ease-out;
}

@keyframes cardDataUpdate {
  0% { transform: translateY(-4px) scale(1.02); }
  50% { transform: translateY(-6px) scale(1.03); box-shadow: 0 16px 40px rgba(0, 0, 0, 0.2); }
  100% { transform: translateY(-4px) scale(1.02); }
}

@keyframes valueUpdate {
  0% { transform: scale(1); }
  30% { transform: scale(1.15); color: #3b82f6; }
  100% { transform: scale(1); }
}

@keyframes chartUpdate {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

/* 成功状态样式 */
.metric-item.success {
  border-color: rgba(16, 185, 129, 0.3);
}

.metric-item.success .metric-background {
  background: transparent;
}

/* 警告状态样式 */
.metric-item.warning {
  border-color: rgba(245, 158, 11, 0.3);
}

.metric-item.warning .metric-background {
  background: transparent;
}

/* 错误状态样式 */
.metric-item.error {
  border-color: rgba(239, 68, 68, 0.3);
}

.metric-item.error .metric-background {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .chart-container {
    width: 120px;
    height: 120px;
  }

  .center-value {
    font-size: 20px;
  }

  .metric-item {
    padding: 12px;
  }

  .metric-value {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-left {
    gap: 12px;
  }

  .header-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .metrics-grid {
    gap: 8px;
  }

  .metric-item {
    padding: 10px;
    gap: 8px;
  }

  .metric-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .metric-icon {
    font-size: 16px;
  }
}
</style>
